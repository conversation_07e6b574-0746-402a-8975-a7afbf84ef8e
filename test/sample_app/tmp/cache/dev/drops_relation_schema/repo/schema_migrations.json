{"digest": "899971CCD11B64E4E4A231156BEF90900AC09F7CED4CBF918D006C33B17FC4A1", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "version"], "type": ["atom", "integer"], "source": ["atom", "version"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}], "source": ["atom", "schema_migrations"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "version"], "type": ["atom", "integer"], "source": ["atom", "version"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": []}, "__struct__": "<PERSON><PERSON><PERSON>"}}