{"digest": "899971CCD11B64E4E4A231156BEF90900AC09F7CED4CBF918D006C33B17FC4A1", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_email_index", "nullable": false}, "name": ["atom", "email"], "type": ["atom", "string"], "source": ["atom", "email"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": ["atom", "first_name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": ["atom", "last_name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": ["atom", "age"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_is_active_index", "nullable": true}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": ["atom", "is_active"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "profile_data"], "type": ["atom", "string"], "source": ["atom", "profile_data"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": [], "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "tags"], "type": ["tuple", [["atom", "array"], ["atom", "any"]]], "source": ["atom", "tags"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "score"], "type": ["atom", "decimal"], "source": ["atom", "score"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "birth_date"], "type": ["atom", "string"], "source": ["atom", "birth_date"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "last_login_at"], "type": ["atom", "string"], "source": ["atom", "last_login_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "users"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": [{"attributes": {"name": ["atom", "users_is_active_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_is_active_index", "nullable": true}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": ["atom", "is_active"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_last_name_first_name_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": ["atom", "first_name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": ["atom", "last_name"]}, "__struct__": "Field"}], "composite": true, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_email_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "users_email_index", "nullable": false}, "name": ["atom", "email"], "type": ["atom", "string"], "source": ["atom", "email"]}, "__struct__": "Field"}], "composite": false, "unique": true}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}