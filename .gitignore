# The directory Mix will write compiled artifacts to.
/_build/

# If you run "mix test --cover", coverage assets end up here.
/cover/

# The directory Mix downloads your dependencies sources to.
/deps/

# Where third-party dependencies like ExDoc output generated docs.
/doc/

# If the VM crashes, it generates a dump, let's ignore it too.
erl_crash.dump

# Also ignore archive artifacts (built via "mix archive.build").
*.ez

# Ignore package tarball (built via "mix hex.build").
drops_relation-*.tar

# Temporary files, for example, from tests.
/tmp/

priv/*.db*
priv/repo/*sqlite*

test/sample_app/_build
test/sample_app/deps
test/sample_app/priv/*db*
