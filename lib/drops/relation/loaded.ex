defmodule Drops.Relation.Loaded do
  @moduledoc """
  Represents a loaded paginated result set with metadata.

  This struct contains both the loaded data and pagination metadata, making it
  easy to work with paginated results and generate navigation elements.

  ## Structure

  - `data` - List of loaded records
  - `page` - Current page number (1-based)
  - `per_page` - Number of records per page
  - `total_count` - Total number of records in the dataset
  - `total_pages` - Total number of pages
  - `has_next` - Whether there is a next page
  - `has_prev` - Whether there is a previous page

  ## Examples

      # Basic usage
      loaded = Users.page(2) |> Users.per_page(10)

      # Access data
      users = Enum.to_list(loaded)
      first_user = Enum.at(loaded, 0)

      # Access metadata
      loaded.page         # => 2
      loaded.per_page     # => 10
      loaded.total_count  # => 150
      loaded.total_pages  # => 15
      loaded.has_next     # => true
      loaded.has_prev     # => true

      # Generate navigation
      if loaded.has_prev do
        link("Previous", to: "/users?page=\#{loaded.page - 1}")
      end

      if loaded.has_next do
        link("Next", to: "/users?page=\#{loaded.page + 1}")
      end

  ## Enumerable Protocol

  The Loaded struct implements the Enumerable protocol, allowing you to use
  all Enum functions directly on paginated results:

      loaded = Users.page(1)

      # Use Enum functions
      names = Enum.map(loaded, & &1.name)
      count = Enum.count(loaded)
      first = Enum.at(loaded, 0)

      # Convert to list
      users_list = Enum.to_list(loaded)

  ## Navigation Helpers

  The struct provides convenient boolean flags for navigation:

      # Check if navigation is needed
      if loaded.total_pages > 1 do
        render_pagination(loaded)
      end

      # Generate page range
      start_page = max(1, loaded.page - 2)
      end_page = min(loaded.total_pages, loaded.page + 2)
      page_range = start_page..end_page
  """

  @type t :: %__MODULE__{
          data: [struct()],
          meta: map()
        }

  defstruct [
    :data,
    :meta
  ]

  @doc """
  Creates a new Loaded struct with the given data and metadata.

  ## Parameters

  - `data` - List of loaded records
  - `meta` - Metadata map containing plugin-specific information

  ## Returns

  Returns a Loaded struct with the provided data and metadata.

  ## Examples

      data = [%User{id: 1}, %User{id: 2}]
      meta = %{pagination: %{page: 1, per_page: 10, total_count: 25}}
      loaded = Drops.Relation.Loaded.new(data, meta)

      loaded.data                           # => [%User{id: 1}, %User{id: 2}]
      loaded.meta.pagination.page          # => 1
      loaded.meta.pagination.per_page      # => 10
      loaded.meta.pagination.total_count   # => 25
  """
  @spec new([struct()], map()) :: t()
  def new(data, meta \\ %{}) when is_list(data) and is_map(meta) do
    %__MODULE__{
      data: data,
      meta: meta
    }
  end
end

# Enumerable protocol implementation for Loaded
defimpl Enumerable, for: Drops.Relation.Loaded do
  @moduledoc """
  Enumerable protocol implementation for Drops.Relation.Loaded.

  This allows Loaded structs to be used with all Enum functions,
  operating on the loaded data while preserving pagination metadata.
  """

  def count(%Drops.Relation.Loaded{data: data}) do
    {:ok, length(data)}
  end

  def member?(%Drops.Relation.Loaded{data: data}, element) do
    {:ok, element in data}
  end

  def slice(%Drops.Relation.Loaded{data: data}) do
    size = length(data)
    {:ok, size, fn start, count, _step -> Enum.slice(data, start, count) end}
  end

  def reduce(%Drops.Relation.Loaded{data: data}, acc, fun) do
    Enumerable.List.reduce(data, acc, fun)
  end
end
