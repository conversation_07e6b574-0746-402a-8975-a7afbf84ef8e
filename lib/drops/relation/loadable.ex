defprotocol Drops.Relation.Loadable do
  @moduledoc """
  Protocol for loading data from queryable structs with metadata support.

  This protocol defines how different types of queryable structs should be loaded,
  allowing plugins to provide metadata that gets included in the resulting
  `Drops.Relation.Loaded` struct.

  ## Implementation

  The protocol expects implementations to return a `Drops.Relation.Loaded` struct
  that contains both the loaded data and any metadata provided by plugins.

  ## Examples

      # Basic loading without metadata
      loaded = Drops.Relation.Loadable.load(Users.restrict(active: true))

      # Loading with pagination metadata
      paginated = Users.per_page(10) |> Users.page(1)
      loaded = Drops.Relation.Loadable.load(paginated)
      loaded.meta.pagination.page         # => 1
      loaded.meta.pagination.per_page     # => 10
      loaded.meta.pagination.total_count  # => 150

  ## Plugin Integration

  Plugins can contribute metadata by storing it in the queryable struct's `meta` field.
  The metadata is then included in the `Loaded` struct when `load/1` is called.

      # Plugin stores metadata in queryable.meta
      queryable = %SomeRelation{
        meta: %{
          pagination: %{page: 1, per_page: 10, total_count: 150},
          custom_plugin: %{some_data: "value"}
        }
      }

      loaded = Drops.Relation.Loadable.load(queryable)
      # loaded.meta contains all plugin metadata
  """

  @doc """
  Loads data from a queryable struct and returns a Loaded struct with metadata.

  ## Parameters

  - `queryable` - A queryable struct that can be loaded

  ## Returns

  Returns a `Drops.Relation.Loaded` struct containing:
  - `data` - The loaded records
  - `meta` - Metadata provided by plugins

  ## Examples

      # Load a basic query
      users = Users.restrict(active: true)
      loaded = Drops.Relation.Loadable.load(users)

      # Load with pagination
      paginated = Users.per_page(10) |> Users.page(1)
      loaded = Drops.Relation.Loadable.load(paginated)
  """
  @spec load(struct()) :: Drops.Relation.Loaded.t()
  def load(queryable)
end
