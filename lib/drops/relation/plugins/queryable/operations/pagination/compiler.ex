defmodule Drops.Relation.Plugins.Queryable.Operations.Pagination.Compiler do
  @moduledoc """
  Compiler for pagination operations.

  This compiler is a no-op since pagination is handled directly in the
  Pagination plugin rather than being compiled into Ecto queries.
  The pagination metadata is stored in the relation struct and used
  when executing the pagination functions.
  """

  alias Drops.Relation.Plugins.Queryable.Operations.Compiler

  use Compiler

  @doc """
  Visits pagination operations and returns the query unchanged.

  Pagination operations don't modify the Ecto query itself - they're
  handled at execution time by adding LIMIT and OFFSET clauses.

  ## Parameters

  - `relation` - The relation struct containing pagination metadata
  - `context` - The compilation context with query and options

  ## Returns

  Returns `{:ok, query}` with the query unchanged.
  """
  def visit(_relation, %{query: query}) do
    {:ok, query}
  end
end
