[22m
12:53:49.692 [info] Cleared schema cache for repository: Test.Repos.Sqlite
[0m[36m
12:53:49.694 [debug] Schema cache miss for Elixir.Test.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[32m
12:53:49.768 [debug] QUERY ERROR source="users" db=0.8ms queue=1.0ms
INSERT INTO "users" DEFAULT VALUES RETURNING "id" []
[0m[36m
12:54:11.381 [debug] Schema cache miss for Elixir.Test.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[32m
12:54:11.466 [debug] QUERY ERROR source="users" db=1.0ms queue=1.3ms
INSERT INTO "users" DEFAULT VALUES RETURNING "id" []
[0m