ARG ELIXIR_VERSION=1.18.4
ARG OTP_VERSION=27.3.4.1
ARG DISTRO=debian-bookworm-20250630

ARG IMAGE="hexpm/elixir:${ELIXIR_VERSION}-erlang-${OTP_VERSION}-${DISTRO}"

FROM ${IMAGE}

# Fix GPG issues and update package lists
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get update --allow-releaseinfo-change --allow-unauthenticated -y && \
    apt-get install -y --no-install-recommends --allow-unauthenticated \
    ca-certificates gnupg && \
    apt-get update --allow-releaseinfo-change -y && \
    apt-get install -y --no-install-recommends \
    git bash less inotify-tools ssh \
    sqlite3

# For PG
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    dirmngr ca-certificates software-properties-common apt-transport-https lsb-release curl

RUN curl -fSsL https://www.postgresql.org/media/keys/ACCC4CF8.asc | \
    gpg --dearmor | \
    tee /usr/share/keyrings/postgresql.gpg > /dev/null

RUN echo "deb [arch=amd64,arm64,ppc64el signed-by=/usr/share/keyrings/postgresql.gpg] \
    http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | \
    tee /etc/apt/sources.list.d/postgresql.list

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    postgresql-client-16 postgresql-16

WORKDIR /workspace/drops-relation
