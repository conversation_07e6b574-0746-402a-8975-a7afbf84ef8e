{
  "name": "drops-relation-latest",
  "dockerComposeFile": "docker-compose.yml",
  "service": "dev-latest",
  // "service": "dev-1.15",
  // "service": "dev-1.14",
  // "service": "dev-1.13",
  "runServices": [
    "dev-latest"
  ],
  "workspaceFolder": "/workspace/drops-relation",
  "features": {
    "ghcr.io/devcontainers/features/github-cli": {},
    "ghcr.io/nils-geistmann/devcontainers-features/zsh:0": {},
    "ghcr.io/devcontainers-extra/features/npm-packages:1": {},
    "ghcr.io/rocker-org/devcontainer-features/apt-packages:1": {
      "packages": "inotify-tools npm"
    }
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "sleistner.vscode-fileutils",
        "kahole.magit",
        "JakeBecker.elixir-ls",
        "Augment.vscode-augment",
        "GitHub.vscode-pull-request-github",
        "GitHub.copilot"
      ],
      "settings": {
        "terminal.integrated.shell.linux": "/usr/local/bin/zsh",
        "editor.formatOnSave": true
      },
      "postCreateCommand": "cd ~/dotfiles && git pull --rebase && ./install.sh"
    }
  }
}
