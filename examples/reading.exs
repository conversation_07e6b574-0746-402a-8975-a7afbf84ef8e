defmodule Users do
  use Drops.Relation, repo: Test.Repos.Postgres

  schema("users", infer: true)
end

Enum.each(Users.all(), &Users.delete/1)

{:ok, user} = Users.insert(%{name: "<PERSON>", active: true})

Users.insert(%{name: "<PERSON>", active: true})
Users.insert(%{name: "<PERSON>", active: false})
Users.insert(%{name: "<PERSON>", active: true})

# Common functions known from Ecto.Repo
Users.get(user.id)
Users.get!(user.id)

Users.get_by(name: "<PERSON>")
Users.get_by!(name: "<PERSON>")

Users.all()
Users.all_by(active: true)

# Additional functions for covenience
Users.first()
Users.last()
Users.count()

# Composable `restrict` function
Users.restrict(name: "<PERSON>") |> Users.restrict(active: true)

# Composable `order` function
Users.restrict(active: true) |> Users.order(:name)
