---
type: "always_apply"
---

# Documentation Style Guide

1. Function Head Documentation
Each function head gets its own @doc with complete documentation
No function signature listings - document each head individually
Use descriptive function descriptions that explain the purpose

2. Parameter Documentation
List each parameter with clear descriptions
Include parameter types and constraints
Use consistent formatting: - parameter_name - Description of what it does

3. Return Value Documentation
Always document what the function returns
Include success and error cases where applicable
Use consistent formatting: ## Returns section

4. @spec Annotations
Every public function must have comprehensive @spec
Use proper Elixir types
Include union types for error cases

5. Examples
Provide multiple examples showing different use cases
Include both simple and complex scenarios
Show chaining and composition where applicable
